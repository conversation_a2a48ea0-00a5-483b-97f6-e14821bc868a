package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASHttpClientConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.OptimisticLockRetryHandlerImpl;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.StatusCodeRetryHandlerImpl;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WASRetryHandler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExternalTaskSuccess;
import java.util.Arrays;
import java.util.Collections;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

/** <AUTHOR> */
public class WASHttpClientTest {

  @Mock private RestTemplate restTemplate;
  @Mock private StatusCodeRetryHandlerImpl defaultRetryHandlerImpl;
  @Mock private OptimisticLockRetryHandlerImpl optimisticLockRetryHandlerImpl;
  @Mock private WASHttpClientConfiguration wasHttpClientConfiguration;

  @InjectMocks private WASHttpClient wasHttpClient;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    WASRetryHandler.addHandler(RetryHandlerName.STATUS_CODE, defaultRetryHandlerImpl);
    WASRetryHandler.addHandler(RetryHandlerName.OPTIMISTIC_LOCK, optimisticLockRetryHandlerImpl);
    // Default whitelist for most tests
    Mockito.when(wasHttpClientConfiguration.getWhitelistedDomains())
            .thenReturn(Arrays.asList("intuit.com", "quickbooks.com"));
  }

  @Test
  public void testFailure() {
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest =
            WASHttpRequest.<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse>builder()
                    .url(actionRequest.getEndpoint())
                    .request(actionRequest)
                    .httpMethod(HttpMethod.POST)
                    .build();

    Mockito.when(
                    restTemplate.exchange(
                            actionRequest.getEndpoint(),
                            HttpMethod.POST,
                            new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                            wasHttpRequest.getResponseType()))
            .thenThrow(new RestClientException("error in making call"));

    try{
      wasHttpClient.httpResponse(wasHttpRequest);

    }catch (IllegalArgumentException e){

      Assert.assertEquals("HTTP URL must not be null", e.getMessage());

    }

  }

  @Test
  public void testSuccess() {
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WorkflowTaskHandlerResponse response = new WorkflowTaskHandlerResponse();
    WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest =
            WASHttpRequest.<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse>builder()
                    .url(actionRequest.getEndpoint())
                    .request(actionRequest)
                    .httpMethod(HttpMethod.POST)
                    .build();

    ResponseEntity<WorkflowTaskHandlerResponse> workflowTaskHandlerResponse =
            new ResponseEntity<WorkflowTaskHandlerResponse>(response, HttpStatus.OK);
    Mockito.when(
                    restTemplate.exchange(
                            actionRequest.getEndpoint(),
                            HttpMethod.POST,
                            new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                            wasHttpRequest.getResponseType()))
            .thenReturn(workflowTaskHandlerResponse);
    try{
      wasHttpClient.httpResponse(wasHttpRequest);

    }catch (IllegalArgumentException e){
      Assert.assertEquals("HTTP URL must not be null", e.getMessage());

    }
  }

  @Test
  public void testpostResponseSuccess() {
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WorkflowTaskHandlerResponse response = new WorkflowTaskHandlerResponse();
    ResponseEntity<WorkflowTaskHandlerResponse> workflowTaskHandlerResponse =
            new ResponseEntity<WorkflowTaskHandlerResponse>(response, HttpStatus.OK);

    Mockito.when(
                    restTemplate.exchange(
                            actionRequest.getEndpoint(),
                            HttpMethod.POST,
                            new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                            WorkflowTaskHandlerResponse.class))
            .thenReturn(workflowTaskHandlerResponse);

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
            wasHttpClient.postResponse(
                    actionRequest.getEndpoint(),
                    new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                    WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertTrue(resp.isSuccess2xx() == true);
  }

  @Test
  public void testpostResponseFail() {
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    Mockito.when(
                    restTemplate.exchange(
                            actionRequest.getEndpoint(),
                            HttpMethod.POST,
                            new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                            WorkflowTaskHandlerResponse.class))
            .thenThrow(new RestClientException("error in making call"));

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
            wasHttpClient.postResponse(
                    actionRequest.getEndpoint(),
                    new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                    WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertTrue(resp.isSuccess2xx() == false);
  }

  @Test
  public void testgetResponseSuccess() {
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WorkflowTaskHandlerResponse response = new WorkflowTaskHandlerResponse();
    ResponseEntity<WorkflowTaskHandlerResponse> workflowTaskHandlerResponse =
            new ResponseEntity<WorkflowTaskHandlerResponse>(response, HttpStatus.OK);

    Mockito.when(
                    restTemplate.exchange(
                            actionRequest.getEndpoint(),
                            HttpMethod.GET,
                            null,
                            WorkflowTaskHandlerResponse.class))
            .thenReturn(workflowTaskHandlerResponse);

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
            wasHttpClient.getResponse(
                    actionRequest.getEndpoint(),
                    null,
                    WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertTrue(resp.isSuccess2xx() == true);
  }

  @Test
  public void testgetResponseFail() {
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    Mockito.when(
                    restTemplate.exchange(
                            actionRequest.getEndpoint(),
                            HttpMethod.GET,
                            null,
                            WorkflowTaskHandlerResponse.class))
            .thenThrow(new RestClientException("error in making call"));

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
            wasHttpClient.getResponse(
                    actionRequest.getEndpoint(), null, WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertTrue(resp.isSuccess2xx() == false);
  }

  @Test
  public void testOptimisticLockingRetry() {
    ExternalTaskSuccess request =
            ExternalTaskSuccess.builder().workerId("workerId").variables(Collections.EMPTY_MAP).build();

    WASHttpRequest<ExternalTaskSuccess, Object> wasHttpRequest =
            WASHttpRequest.<ExternalTaskSuccess, Object>builder()
                    .url("url")
                    .request(request)
                    .httpMethod(HttpMethod.POST)
                    .retryHandler(RetryHandlerName.OPTIMISTIC_LOCK)
                    .build();

    Mockito.when(
                    restTemplate.exchange(
                            "url",
                            HttpMethod.POST,
                            new HttpEntity<ExternalTaskSuccess>(request, null),
                            wasHttpRequest.getResponseType()))
            .thenThrow(
                    new HttpServerErrorException(
                            WorkflowConstants.OPTIMISTIC_LOCKING_EXCEPTION,
                            HttpStatus.INTERNAL_SERVER_ERROR,null,null,null,null));
    try{
      wasHttpClient.httpResponse(wasHttpRequest);

    }catch (IllegalArgumentException e){

      Assert.assertEquals("[url] is not a valid HTTP URL", e.getMessage());
    }

  }



  @Test(expected = WorkflowGeneralException.class)
  public void testHttpResponse_withNonWhitelistedDomain_shouldThrow() {
    // Only allow "intuit.com"
    Mockito.when(wasHttpClientConfiguration.getWhitelistedDomains())
            .thenReturn(Arrays.asList("intuit.com"));
    String nonWhitelistedUrl = "https://malicious.com/resource";
    WASHttpRequest<Object, Object> wasHttpRequest = WASHttpRequest.<Object, Object>builder()
            .url(nonWhitelistedUrl)
            .httpMethod(HttpMethod.GET)
            .build();

    wasHttpClient.httpResponse(wasHttpRequest);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testHttpResponse_withMalformedUrl_shouldThrow() {
    String malformedUrl = "http://bad_url";
    WASHttpRequest<Object, Object> wasHttpRequest = WASHttpRequest.<Object, Object>builder()
            .url(malformedUrl)
            .httpMethod(HttpMethod.GET)
            .build();

    wasHttpClient.httpResponse(wasHttpRequest);
  }

}