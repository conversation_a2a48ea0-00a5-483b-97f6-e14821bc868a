package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASHttpClientConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.OptimisticLockRetryHandlerImpl;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.StatusCodeRetryHandlerImpl;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WASRetryHandler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExternalTaskSuccess;
import java.util.Arrays;
import java.util.Collections;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

/** <AUTHOR> */
public class WASHttpClientTest {

  @Mock private RestTemplate restTemplate;
  @Mock private StatusCodeRetryHandlerImpl defaultRetryHandlerImpl;
  @Mock private OptimisticLockRetryHandlerImpl optimisticLockRetryHandlerImpl;
  @Mock private WASHttpClientConfiguration wasHttpClientConfiguration;

  @InjectMocks
  private WASHttpClient wasHttpClient;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    WASRetryHandler.addHandler(RetryHandlerName.STATUS_CODE, defaultRetryHandlerImpl);
    WASRetryHandler.addHandler(RetryHandlerName.OPTIMISTIC_LOCK, optimisticLockRetryHandlerImpl);
    // Default whitelist for most tests
    Mockito.when(wasHttpClientConfiguration.getWhitelistedDomains())
            .thenReturn(Arrays.asList("intuit.com", "quickbooks.com"));
  }

  @Test
  public void testFailure() {
    String testUrl = "https://test.intuit.com/endpoint";
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    Mockito.when(
                    restTemplate.exchange(
                            testUrl,
                            HttpMethod.POST,
                            new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                            WorkflowTaskHandlerResponse.class))
            .thenThrow(new RestClientException("error in making call"));

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
            wasHttpClient.postResponse(
                    testUrl,
                    new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                    WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertFalse(resp.isSuccess2xx());
  }

  @Test
  public void testSuccess() {
    String testUrl = "https://test.intuit.com/endpoint";
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WorkflowTaskHandlerResponse response = new WorkflowTaskHandlerResponse();
    WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest =
            WASHttpRequest.<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse>builder()
                    .url(testUrl)
                    .request(actionRequest)
                    .httpMethod(HttpMethod.POST)
                    .responseType(new ParameterizedTypeReference<WorkflowTaskHandlerResponse>() {})
                    .build();

    ResponseEntity<WorkflowTaskHandlerResponse> workflowTaskHandlerResponse =
            new ResponseEntity<WorkflowTaskHandlerResponse>(response, HttpStatus.OK);
    Mockito.when(
                    restTemplate.exchange(
                            testUrl,
                            HttpMethod.POST,
                            new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                            wasHttpRequest.getResponseType()))
            .thenReturn(workflowTaskHandlerResponse);

    WASHttpResponse<WorkflowTaskHandlerResponse> resp = wasHttpClient.httpResponse(wasHttpRequest);
    Assert.assertTrue(resp.isSuccess2xx());
  }

  @Test
  public void testpostResponseSuccess() {
    String testUrl = "https://test.intuit.com/endpoint";
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WorkflowTaskHandlerResponse response = new WorkflowTaskHandlerResponse();
    ResponseEntity<WorkflowTaskHandlerResponse> workflowTaskHandlerResponse =
            new ResponseEntity<WorkflowTaskHandlerResponse>(response, HttpStatus.OK);

    Mockito.when(
                    restTemplate.exchange(
                            testUrl,
                            HttpMethod.POST,
                            new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                            WorkflowTaskHandlerResponse.class))
            .thenReturn(workflowTaskHandlerResponse);

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
            wasHttpClient.postResponse(
                    testUrl,
                    new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                    WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertTrue(resp.isSuccess2xx());
  }

  @Test
  public void testpostResponseFail() {
    String testUrl = "https://test.intuit.com/endpoint";
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    Mockito.when(
                    restTemplate.exchange(
                            testUrl,
                            HttpMethod.POST,
                            new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                            WorkflowTaskHandlerResponse.class))
            .thenThrow(new RestClientException("error in making call"));

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
            wasHttpClient.postResponse(
                    testUrl,
                    new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
                    WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertFalse(resp.isSuccess2xx());
  }

  @Test
  public void testgetResponseSuccess() {
    String testUrl = "https://test.intuit.com/endpoint";
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WorkflowTaskHandlerResponse response = new WorkflowTaskHandlerResponse();
    ResponseEntity<WorkflowTaskHandlerResponse> workflowTaskHandlerResponse =
            new ResponseEntity<WorkflowTaskHandlerResponse>(response, HttpStatus.OK);

    Mockito.when(
                    restTemplate.exchange(
                            testUrl,
                            HttpMethod.GET,
                            null,
                            WorkflowTaskHandlerResponse.class))
            .thenReturn(workflowTaskHandlerResponse);

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
            wasHttpClient.getResponse(
                    testUrl,
                    null,
                    WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertTrue(resp.isSuccess2xx());
  }

  @Test
  public void testgetResponseFail() {
    String testUrl = "https://test.intuit.com/endpoint";
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    Mockito.when(
                    restTemplate.exchange(
                            testUrl,
                            HttpMethod.GET,
                            null,
                            WorkflowTaskHandlerResponse.class))
            .thenThrow(new RestClientException("error in making call"));

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
            wasHttpClient.getResponse(
                    testUrl, null, WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertFalse(resp.isSuccess2xx());
  }

  @Test
  public void testOptimisticLockingRetry() {
    String testUrl = "https://test.intuit.com/endpoint";
    ExternalTaskSuccess request =
            ExternalTaskSuccess.builder().workerId("workerId").variables(Collections.EMPTY_MAP).build();

    WASHttpRequest<ExternalTaskSuccess, Object> wasHttpRequest =
            WASHttpRequest.<ExternalTaskSuccess, Object>builder()
                    .url(testUrl)
                    .request(request)
                    .httpMethod(HttpMethod.POST)
                    .retryHandler(RetryHandlerName.OPTIMISTIC_LOCK)
                    .responseType(new ParameterizedTypeReference<Object>() {})
                    .build();

    Mockito.when(
                    restTemplate.exchange(
                            testUrl,
                            HttpMethod.POST,
                            new HttpEntity<ExternalTaskSuccess>(request, null),
                            wasHttpRequest.getResponseType()))
            .thenThrow(
                    new HttpServerErrorException(
                            WorkflowConstants.OPTIMISTIC_LOCKING_EXCEPTION,
                            HttpStatus.INTERNAL_SERVER_ERROR,null,null,null,null));

    WASHttpResponse<Object> resp = wasHttpClient.httpResponse(wasHttpRequest);
    Mockito.verify(optimisticLockRetryHandlerImpl).checkAndThrowRetriableException(Mockito.any());
    Assert.assertFalse(resp.isSuccess2xx());
  }



  @Test(expected = WorkflowGeneralException.class)
  public void testHttpResponse_withNonWhitelistedDomain_shouldThrow() {
    // Only allow "intuit.com"
    Mockito.when(wasHttpClientConfiguration.getWhitelistedDomains())
            .thenReturn(Arrays.asList("intuit.com"));
    String nonWhitelistedUrl = "https://malicious.com/resource";
    WASHttpRequest<Object, Object> wasHttpRequest = WASHttpRequest.<Object, Object>builder()
            .url(nonWhitelistedUrl)
            .httpMethod(HttpMethod.GET)
            .responseType(new ParameterizedTypeReference<Object>() {})
            .build();

    wasHttpClient.httpResponse(wasHttpRequest);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testHttpResponse_withMalformedUrl_shouldThrow() {
    String malformedUrl = "http://bad_url";
    WASHttpRequest<Object, Object> wasHttpRequest = WASHttpRequest.<Object, Object>builder()
            .url(malformedUrl)
            .httpMethod(HttpMethod.GET)
            .responseType(new ParameterizedTypeReference<Object>() {})
            .build();

    wasHttpClient.httpResponse(wasHttpRequest);
  }

}