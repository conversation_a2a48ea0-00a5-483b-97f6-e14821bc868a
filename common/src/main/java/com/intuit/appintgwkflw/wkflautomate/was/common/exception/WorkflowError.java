package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
/** <AUTHOR> */
public enum WorkflowError {
  OTHER_TEST("WTESTXXX", "Some Error Occurred due to %s", "Some unknown error has occurred",HttpStatus.INTERNAL_SERVER_ERROR,DownstreamComponentName.WAS,DownstreamServiceName.WAS_GLOBAL_ERROR),
  PROCESS_ALREADY_STARTED("W001", "Process already started", "Process for given request is already started",HttpStatus.INTERNAL_SERVER_ERROR),
  WORKER_EXECUTE_FAILURE("W002", "Failure in executing worker", "Error invoked in executing worker task : %s TID : %s",HttpStatus.INTERNAL_SERVER_ERROR,DownstreamComponentName.CAMUNDA,DownstreamServiceName.CAMUNDA_EXTERNAL_TASK_COMPLETE),
  INVALID_INPUT("W003", WorkflowConstants.INPUT_CANNOT_BE_NULL_OR_EMPTY, WorkflowConstants.INPUT_CANNOT_BE_NULL_OR_EMPTY,HttpStatus.BAD_REQUEST),
  NO_BPMN_FILE("W004", "No BPMN file", "BPMN file is missing", HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS, DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE),
  MISSING_DMN("W005", "Missing DMN", "One or more DMNs are missing", HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS, DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE),
  EXTRA_DMN("W006", "Extra DMN", "One or more DMNs are extra", HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS, DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE),
  INVALID_FILE_FORMAT("W007", "Invalid file format", "Invalid file format", HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS, DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE),
  TEMPLATE_ALREADY_EXISTS("W008","Template already exists", "Template already exists", HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS_DB, DownstreamServiceName.TEMPLATE_DETAILS),
  TEMPLATE_DOES_NOT_EXIST("W009","Template doesn't exist", "Template doesn't exist", HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS_DB, DownstreamServiceName.TEMPLATE_DETAILS),
  TEMPLATE_SAVE_EXCEPTION("W010", "Template save exception", "Template save exception",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS_DB,
          DownstreamServiceName.TEMPLATE_DETAILS),
  TEMPLATE_NOT_FOUND("W011", "Template not found in input", "Template not found",
          HttpStatus.BAD_REQUEST),
  DMN_NOT_FOUND("W012", "Dmn template doesn't exist", "Dmn not found", HttpStatus.BAD_REQUEST),
  BPMN_DOESNT_EXIST("W013", "BPMN doesn't exist", "BPMN doesn't exist", HttpStatus.BAD_REQUEST,
          DownstreamComponentName.WAS, DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE),
  TEMPLATE_READ_EXCEPTION("W014", "Template read exception", "Template read exception",
          HttpStatus.INSUFFICIENT_STORAGE, DownstreamComponentName.WAS_DB, DownstreamServiceName.TEMPLATE_DETAILS),

  DEFINITION_NOT_FOUND("W015", "Definition not found", "Invalid id", HttpStatus.BAD_REQUEST, true),
  PROVIDER_PARSING_EXCEPTION("W016", "Some Error occurred while parsing %s",
          "Error occurred in parsing exception", HttpStatus.INTERNAL_SERVER_ERROR),
  PROVIDER_WRITE_UNSUPPORTED("W017", "Write Method Not Supported.",
          "Unsupported Write Method Exception", HttpStatus.INTERNAL_SERVER_ERROR),
  DEFINITION_DISABLE_NOT_PERMITTED("W018", "Disabling Definition Not permitted.",
          "Process instances are running", HttpStatus.CONFLICT),
  UNSUPPORTED_OPERATION("W019", "Operation Not supported", "Operation Not supported",
          HttpStatus.NOT_ACCEPTABLE),
  CAMUNDA_DEPLOYMENT_FAILED("W020", "Template deployment failed in Camunda. Error=%s",
          "Template deployment failed in Camunda", HttpStatus.INTERNAL_SERVER_ERROR),
  WRITE_FAILED("W021", "Error writing to the file", "Failed to write in file",
          HttpStatus.INTERNAL_SERVER_ERROR),
  UNSUPPORTED_HANDLER_NAME("W022", "Handler name not supported.", "Invalid app connect handler",
          HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_PARAMETER_DETAILS("W023", "parameter details not present.", "Invalid parameter details",
          HttpStatus.INTERNAL_SERVER_ERROR),
  INTERNAL_EXCEPTION("W024", "Some internal exception Occurred",
          "Some internal exception has occurred", HttpStatus.INTERNAL_SERVER_ERROR),
  UNSUPPORTED_HANDLER_DETAILS("W025", "Handler Details not found.", "Invalid handler details",
          HttpStatus.INTERNAL_SERVER_ERROR),
  EXTERNAL_TASK_HANDLER_ERROR("EXTERNAL_TASK_%s_%s_FAILURE", "External task failure in Dependency=%s from Duzzit=%s.Error=%s",
          "Error in executing app connect action", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.APP_CONNECT, DownstreamServiceName.WORKFLOW_TASK_HANDLER),
  STEP_DETAILS_NOT_FOUND("W027", "Error while Reading Step Details",
          "Step Details Missing Exception", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS, DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE),
  INVALID_DEFINITION_DETAILS("W028", "Exception in getting definition details for id %s",
          "Error in getting definition details", HttpStatus.NOT_FOUND, DownstreamComponentName.WAS_DB,
          DownstreamServiceName.WAS_DB_GET_DEFINITIONS),
  EMPTY_BPMN_EXCEPTION("W029", "Error BPMN Example is null/empty", "BPMN XML Null/Empty Exception",
          HttpStatus.INTERNAL_SERVER_ERROR),
  DEFINITION_DELETE_NOT_PERMITTED("W030", "Deleting Definition Not permitted.",
          "Process instances are running or definition is running", HttpStatus.CONFLICT),
  WORKFLOW_AUTH_DETAILS_ERROR("W031", "Error while getting auth details for ownerId %s",
          "Exception in fetching auth details", HttpStatus.INTERNAL_SERVER_ERROR),
  GET_SUBSCRIPTION_FAIL("W032", "Error in getting subscription details for realm %s",
          "Get subscription error", HttpStatus.INTERNAL_SERVER_ERROR),
  CREATE_WORKFLOW_FAIL("W033", "Error in creating workflow for definition=%s. Error=%s",
          "Create workflow error", HttpStatus.INTERNAL_SERVER_ERROR),
  ACTIVATE_DEACTIVATE_WORKFLOW_FAIL("W034", "Error in activating/deactivating workflow for workflowId=%s. Error=%s",
          "Activate/Deactivate workflow error", HttpStatus.INTERNAL_SERVER_ERROR),
  AUTH_DETAILS_NOT_FOUND("W035", "AuthDetails not found", "AuthDetails not found",
          HttpStatus.INTERNAL_SERVER_ERROR),
  DEFINITION_SAVE_ERROR("W036", "Exception while saving the definition in DB",
          "Not able to save defintition in database", HttpStatus.INTERNAL_SERVER_ERROR),
  UPDATE_WORKFLOW_FAIL("W037", "Error in Updating workflow for definition=%s. Error=%s",
          "Update workflow error", HttpStatus.INTERNAL_SERVER_ERROR),
  DELETE_WORKFLOW_FAIL("W038", "Error in Deleting workflow for workflowId=%s. Error=%s",
          "Delete workflow error", HttpStatus.INTERNAL_SERVER_ERROR),
  CREATE_UPDATE_WORKFLOW_FAIL("W039", "Error in creating/updating workflow for definition=%s",
          "Create/Update workflow error", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_WORKFLOW_ID_INPUT("W040", "Input Workflow id cannot be null or empty",
          "Input Workflow id  cannot be null or empty", HttpStatus.BAD_REQUEST),
  BPMN_DEFINITION_NOT_FOUND("W041", "BPMN Definition not found",
          "No Definitions Available by given RealmId", HttpStatus.NOT_FOUND),
  ASYNC_RESPONSE_TIMEOUT("W042", "AsyncResponseTimeout cannot be null",
          "AsyncResponseTimeout cannot be null", HttpStatus.BAD_REQUEST),
  UPDATE_PROCESS_STATUS_FAILURE("W043", "Failure to update process status for given processId=%s",
          "Failure in updating process status", HttpStatus.INTERNAL_SERVER_ERROR),
  MISSING_APP_ID("W044", "App id missing in authorization",
          "App id can not be null or empty exception", HttpStatus.BAD_REQUEST),
  PROCESSING_ERROR("W045", "Processing error", "Processing  exception",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_TRIGGER_RULE_HANDLER),
  MULTIPLE_DEFINITION_ERROR("W046", "Multiple definition found", "Multiple definition exception",
          HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_INPUT_FOR_CREATING_RULES("W047", "Invalid Input For Creating Rules",
          "Invalid Rule Input Exception", HttpStatus.INTERNAL_SERVER_ERROR),
  CURRENT_STEP_DETAILS_NOT_FOUND("W048", "Error while Reading Current Step Details",
          "Current Step Details Missing Exception", HttpStatus.NOT_FOUND),
  DEFINITION_UPDATE_ERROR("W049", "Exception while updating the definition in DB",
          "Not able to update definition in database", HttpStatus.INTERNAL_SERVER_ERROR),
  DEFINITION_ALREADY_EXISTS("W050", "Error Definition Already Exists for Template",
          "Definition Already Exists Exception", HttpStatus.INTERNAL_SERVER_ERROR, true),
  UPDATE_DEFINITION_STATUS_FAILED("W051", "Process definition update in camunda failed. Error=%s",
          "Exception while updating status in camunda", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.CAMUNDA, DownstreamServiceName.UPDATE_DEFINITION_STATUS),
  DELETE_DEFINITION_FAILED("W052", "Process definition delete in camunda failed. Error=%s",
          "Exception while deleting definition in camunda", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.CAMUNDA, DownstreamServiceName.UPDATE_DEFINITION_STATUS),
  ENABLE_DISABLE_DEFINITION_STATUS_FAILED("W053", "Process definition enable/disable failed",
          "Exception while enabling/disabling definition in appconnect",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS_DB,
          DownstreamServiceName.WAS_DISABLE_DEFINITION),
  CONCURRENT_UPDATE_DEFINITION_FAILED("W054",
          "Trying to update Obsolete Definition. Please try again!",
          "Exception while Trying to update Obsolete Definition. Please try again!",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS_DB,
          DownstreamServiceName.WAS_UPDATE_DEFINITION),
  DELETE_ALL_WORKFLOWS_FAILED("W055", "delete all workflows failed for given company",
          "Exception while deleting all workflows", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS_DB, DownstreamServiceName.WAS_DOWNGRADE),
  UNSUBSCRIBE_WORKFLOW_FAIL("W056", "Error in unsubscribing workflow for given company. Error=%s",
          "unsbuscribe workflow error", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.APP_CONNECT, DownstreamServiceName.UNSUBSCRIBE_APPCONNECT),
  EXTERNAL_TASK_FOR_DEFINITION_MARKED_DELETED_OR_DISABLED("W057",
          "Trying to run an external task for a definition marked for delete or disbaled",
          "Marking external task as failed", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.CAMUNDA, DownstreamServiceName.CAMUNDA_EXTERNAL_TASK_FAILURE),
  ENABLED_DEFINITION_ALREADY_EXISTS("W058",
          "Enabled Workflow definition already exists with name %s",
          "Error in enabling workflow due to existing already enabled workflow for template",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_ENABLE_DEFINITION),
  INVALID_TEMPLATE_DETAILS("W059", "Invalid Template Details", "Error Invalid Template Details",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_ENABLE_DEFINITION),
  INVALID_TRIGGER_DETAILS("W060", "Invalid Trigger Details", "Error while fetching Trigger Details",
          HttpStatus.INTERNAL_SERVER_ERROR),
  PROVIDER_READ_ONE_UNSUPPORTED("W061", "Read one Method Not Supported.",
          "Unsupported Read one Method Exception", HttpStatus.INTERNAL_SERVER_ERROR),
  PROVIDER_READ_LIST_UNSUPPORTED("W062", "Read List Method Not Supported.",
          "Unsupported Read List Method Exception", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_WORKFOW_DELETE_DETAILS("W063", "Invalid Delete details.",
          "Invalid details for deleting all worlflows", HttpStatus.BAD_REQUEST),
  INPUT_INVALID("W0064", "Input %s cannot be null or empty", WorkflowConstants.INPUT_CANNOT_BE_NULL_OR_EMPTY,
          HttpStatus.BAD_REQUEST),
  UNKNOWN_ERROR("W0065", "Some unknown error occured", "some unknown error occured",
          HttpStatus.BAD_REQUEST),
  PROCESS_NOT_FOUND("W066", "Process not found", "Invalid process id", HttpStatus.BAD_REQUEST,
          DownstreamComponentName.WAS_DB, DownstreamServiceName.WAS_DB_GET_PROCESS),
  PROCESS_IN_ERROR_STATE("W067", "Process is in error state", "process status is error",
          HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS_DB,
          DownstreamServiceName.WAS_DB_GET_PROCESS),
  DELETE_DEPLOYMENT_FAILED("W068", "Definition deployment delete in camunda failed. Error=%s",
          "Exception while deleting deployment in camunda", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.CAMUNDA, DownstreamServiceName.CAMUNDA_DELETE_DEFINITION),
  CREATE_SUBSCRIPTION_FAIL("W069", "Error in creating subscription details for realm=%s. Error=%s",
          "Create subscription error", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.APP_CONNECT, DownstreamServiceName.CREATE_SUBSCRIPTION_DETAILS),
  DISABLE_DELETE_WORKFLOW_ERROR("W070", "Error while trying execute disable_delete_workflow task",
          "DisableDelete workflow error", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.APP_CONNECT, DownstreamServiceName.DISABLE_DELETE_WORKFLOW),
  OPERATION_NOT_PERMITTED("W071", "Operation Not permitted.",
          "Operation not allowed on this definition", HttpStatus.METHOD_NOT_ALLOWED),
  ENABLED_DEFINITION_NOT_FOUND("W072", "Enabled definition not found", "No enabled definition",
          HttpStatus.BAD_REQUEST, true),
  CAMUNDA_COMPLETE_TASK_FAILED("W073", "Error while completing task in Camunda. Error=%s",
          "Error while completing task in Camunda", HttpStatus.INTERNAL_SERVER_ERROR),
  CAMUNDA_FAILURE_TASK_FAILED("W074", "Error while marking task as failed in Camunda. Error=%s",
          "Error while marking task as failed in Camunda", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_WORKFLOW_ERROR_INPUT("W075", "Workflow Error name cannot be null or empty",
          "Workflow Error cannot be null or empty", HttpStatus.BAD_REQUEST),
  CAMUNDA_INCIDENT("W076", "Creating an incident in camunda", "Creating an incident in camunda",
          HttpStatus.INTERNAL_SERVER_ERROR),
  CAMUNDA_TASK_NOT_FOUND("W077", "Camunda external task not found. Error=%s",
          "Camunda external task not found", HttpStatus.NOT_FOUND),
  INVALID_OFFLINE_TICKET("W078", "Offline ticket Expired or Offline ticket is null or empty", "Offline ticket Expired or Offline ticket is null or empty",HttpStatus.BAD_REQUEST),
  INVALID_REALM_ID("W079", "Input Realm id cannot be null or empty.",
          "Input Realm id cannot be null or empty", HttpStatus.BAD_REQUEST),
  INVALID_APPCONNECT_SUBSCRIPTION_ID("W080", "Input App-Connect Subscription id cannot be null or empty.",
          "Input App-Connect Subscription id cannot be null or empty", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_ENTITY_ID("W081", "Entity Id cannot be null or empty.", "Entity Id cannot be null or empty.",
          HttpStatus.BAD_REQUEST),
  INVALID_BPMN_MODEL_INSTANCE("W082", "BPMN Model Instance cannot be null or empty",
          "BPMN Model Instance cannot be null or empty", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_DEFINITION_ID("W083", "Input Definition id cannot be null or empty.",
          "Input Definition id cannot be null or empty", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_ACTION_KEY("W084", "Action Key is null or empty", "Action Key is null or empty",HttpStatus.BAD_REQUEST),
  EXTERNAL_TASK_ACCESS_FAILURE("W085", "Access Exception in External Task handle in Dependency=%s from Duzzit=%s.Error=%s",
          "Access Exception in External Task handle", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.APP_CONNECT, DownstreamServiceName.WORKFLOW_TASK_HANDLER),
  RULES_NOT_FOUND("W086", "Rules are null or empty", "Rules are null or empty",HttpStatus.BAD_REQUEST),
  DISPLAY_NAME_NOT_FOUND("W087", "Display Name is null or empty", "Display Name is null or empty",HttpStatus.BAD_REQUEST),
  INVALID_PARAMETER_NAME("W088", "Parameter Name is invalid", "Parameter Name is invalid",HttpStatus.BAD_REQUEST),
  DEFINITION_NAME_ALREADY_EXISTS("W089","Definition Name Already Exists","Definition Name Already Exists",HttpStatus.BAD_REQUEST),
  DATATYPE_NOT_FOUND("W090", "Data Type not found", "Data Type not found", HttpStatus.BAD_REQUEST),
  CLOSE_TASK_ACTION_NOT_FOUND(
          "W091", "Close task action not found", "Close task action not found", HttpStatus.BAD_REQUEST),
  INPUT_PARAMETERS_NOT_FOUND(
          "W092", "Input Parameters not found", "Input Parameters not found", HttpStatus.BAD_REQUEST),
  INVALID_RECORD_TYPE(
          "W093", "Record not found", "Record not found", HttpStatus.BAD_REQUEST),
  ACTION_NOT_FOUND(
          "W094", "Action not found", "Action not found", HttpStatus.BAD_REQUEST),
  MESSAGE_EVENT_DEFINITION_NOT_FOUND(
          "W095", "Message Event Definition not found", "Message Event Definition not found", HttpStatus.BAD_REQUEST),
  HANDLER_PARAMETER_NOT_FOUND(
          "W096", "Handler Parameters not found", "Handler Parameters not found", HttpStatus.BAD_REQUEST),
  TASK_DETAILS_NOT_FOUND(
          "W097", "Task Details not found", "Task Details not found", HttpStatus.BAD_REQUEST),
  HANDLER_DETAILS_NOT_FOUND(
          "W098", "Handler Details not found", "Handler Details not found", HttpStatus.BAD_REQUEST),
  REQUEST_IN_PROGRESS("W099","The request has been accepted for processing.","Request in Progress",HttpStatus.ACCEPTED, true),
  TRIGGER_DETAILS_NOT_FOUND("W100","Trigger Details not found","Trigger Details not found",HttpStatus.BAD_REQUEST),

  INVALID_TASK_HANDLER_DETAILS("W102", "Task Handler name not present in handler details.", "Task Handler name not present in handler details.",
          HttpStatus.BAD_REQUEST),
  INVALID_ACTION_DETAILS("W103", "Action name not present in handler details.", "Action name not present in handler details.",
          HttpStatus.BAD_REQUEST),
  INVALID_BPMN_PROCESS_DETAILS("W104", "Process details not found in BPMN.", "Process details not found in BPMN.",
          HttpStatus.BAD_REQUEST),
  TASK_DETAIL_SAVE_EXCEPTION("W105", "Task Details save exception", "Task Details save exception",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS_DB,
          DownstreamServiceName.WAS_DB_SAVE_TASK_DETAILS),
  ERROR_PROCESSING_TASK_DETAILS("W106", "Error while processing task details.", "Error while processing task details.",
          HttpStatus.BAD_REQUEST),
  UNSUPPORTED_FILTER_EXPRESSION("W107", "Unsupported filter expression", "Filter expression can only be simple filter expression or compound expressions",
          HttpStatus.BAD_REQUEST),
  WORKER_NOT_INITIALIZE(
          "W108", "Worker not initialized", "Worker not initialized", HttpStatus.BAD_REQUEST),

  INVALID_GRAPHQL_AUTH_TYPE(
          "W109",
          "Graphql Auth type not supported",
          "Graphql Auth type not supported",
          HttpStatus.BAD_REQUEST),
  HUMAN_TASK_DOWNSTREAM_FAILURE("W110", "Error in create/update/read project. Error=%s",
          "Create/Update project exception", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.PROJECT_SERVICE, DownstreamServiceName.PROJECT_SERVICE_CREATE_UPDATE_PROJECT),
  INVALID_TASK_TXN_ID("W111", "Transaction-id cannot be null while updating project",
          "Transaction-id cannot be null while updating project", HttpStatus.BAD_REQUEST),
  ERROR_PROCESSING_WORKFLOW_TASK("W112", "Error while processing workflow task.", "Error while processing workflow task.",
          HttpStatus.BAD_REQUEST),
  ERROR_NO_TASK_HANDLER_REGISTERED("W113", "No task handler registered.", "No task handler registered.",
          HttpStatus.BAD_REQUEST),
  SENDING_NOTIFICATION_FAILED(
          "114", "Failure in sending notification", "Failure in sending notification", HttpStatus.INTERNAL_SERVER_ERROR),
  ACTIVITY_DETAIL_NOT_FOUND("W115", "Error while processing workflow task. Activity definition detail not found.",
          "Error while processing workflow task. Activity definition detail not found.", HttpStatus.BAD_REQUEST),
  REGION_INACTIVE_ERROR("W116", "Region is inactive. API calls will fail", "Region is inactive. API calls will fail", HttpStatus.INTERNAL_SERVER_ERROR),
  ACTIVITY_PROGRESS_DETAIL_NOT_FOUND("W117", "Error while processing workflow task. ActivityProgressDetail detail not found in DB.",
          "Error while processing workflow task. ActivityProgressDetail detail not found in DB.", HttpStatus.BAD_REQUEST),
  TRANSACTION_DETAIL_NOT_FOUND("W118", "Error while processing workflow task. Transaction detail not found in DB.",
          "Error while processing workflow task. Transaction detail not found in DB.", HttpStatus.BAD_REQUEST),
  IUS_GET_PERSONA_ERROR("W119", "Error while getting persona details from IUS", "Error while getting persona details from IUS",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.IUS, DownstreamServiceName.IUS_GET_PERSONA_DETAILS),
  ACCESS_DENIED_FOR_CLIENT_APP_ID("W120", "Access is denied for the given AppId", "Access is denied for the given AppId", HttpStatus.BAD_REQUEST),
  API_ACCESS_BLOCKED("W121", "Access is blocked for the API", "Access is blocked for the API", HttpStatus.BAD_REQUEST),
  UNSUPPORTED_LOOKUP_FILTER_QUERY("W122", "Unsupported lookup filter expression", "Incorrect value of lookup key or value", HttpStatus.BAD_REQUEST),
  INCORRECT_LOOKUP_FILTER_QUERY("W123", "Incorrect lookup filter expression", "Incorrect order of lookup key or value", HttpStatus.BAD_REQUEST),
  CIRCUIT_OPEN_ERROR("W124", "Call failed as circuit is not closed. Reason=%s", "Call failed as circuit is not closed", HttpStatus.INTERNAL_SERVER_ERROR),
  WORKFLOW_NOT_FOUND("W125", "Workflow Details not found. Reason=%s", "Invalid Workflow id", HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS_DB, DownstreamServiceName.WAS_DB_GET_PROCESS),
  INVALID_ACTIVITY_INSTANCE_ID("W126", "Activity Instance Id not found", "Invalid Activity Instance id", HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS_DB, DownstreamServiceName.WAS_DB_TASK_MUTATION),
  INVALID_TASK_INPUT("W127", "Invalid Task Input. Reason=%s", "Invalid Task Input", HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS_DB, DownstreamServiceName.WAS_DB_TASK_MUTATION),
  REGISTER_TOKEN_FAIL("W128", "Error during registering token on app-connect",
          "Register token fail", HttpStatus.INTERNAL_SERVER_ERROR),
  EXTERNAL_TASK_THRESHOLD_BREACHED("W129", "Activity executed too many times for activityId=%s", "Too many external tasks created for activity", HttpStatus.TOO_MANY_REQUESTS),
  CAMUNDA_EXTEND_LOCK_FAILED("W130", "Error while extending lock on task in Camunda. Error=%s",
          "Error while extending lock on task in Camunda", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_VARIABLE_VALUE("W131", "Error while serialising due to invalid variable value", "Error while serialising due to invalid variable value", HttpStatus.BAD_REQUEST),
  DMN_V1_3_TEMPLATE_FAILURE("W132", "DMN version not supported. Upgrade to DMN 1.3 ",
          "DMN version not supported. Upgrade to DMN 1.3 ", HttpStatus.INTERNAL_SERVER_ERROR),
  DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME_THRESHOLD_BREACHED("W133", "Too many definitions created/updated in timeframe for workflow=%s", "Too many definitions created/updated for workflow", HttpStatus.TOO_MANY_REQUESTS),
  DEFINITIONS_PER_WORKFLOW_PER_REALM_THRESHOLD_BREACHED("W134", "Too many definitions created/updated for workflow=%s by the realm=%s", "Too many definitions created/updated for workflow by a realm", HttpStatus.TOO_MANY_REQUESTS),
  MANDATORY_TASK_PARAMS_MISSING("W135", "Mandatory task params missing.",
          "Mandatory task params missing.", HttpStatus.INTERNAL_SERVER_ERROR),
  CAMUNDA_TASK_SUSPENDED_COMPLETE_FAILED("W136", "Error completing task for suspended process in Camunda. Error=%s",
          "Error completing task for suspended process in Camunda.", HttpStatus.INTERNAL_SERVER_ERROR, true),
  CAMUNDA_TASK_SUSPENDED_FAILURE_FAILED("W137", "Error marking task for suspended process as failed in Camunda. Error=%s",
          "Error marking task for suspended process as failed in Camunda.", HttpStatus.INTERNAL_SERVER_ERROR, true),
  IUS_AUTHORIZATION_DELEGATION_ERROR("W138", "Error while authorizing delegation for offline ticket", "Error while authorizing delegation for offline ticket",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.IUS, DownstreamServiceName.IUS_AUTHORIZATION_DELEGATION),
  MULTIPLE_CALLED_PROCESSES_FOUND("W139", "Multiple called processes found", "Multiple called processes found", HttpStatus.BAD_REQUEST,
          DownstreamComponentName.WAS_DB, DownstreamServiceName.WAS_DB_GET_PROCESS),
  CALLED_DEFINITION_NOT_FOUND("W140", "Called Definition not found", "Invalid called definition id", HttpStatus.BAD_REQUEST, true),
  APPROVAL_SERVICE_CALL_FAILED(
          "W141", "Failure in Approval Service downstream call", "Failure in Approval Service downstream call", HttpStatus.INTERNAL_SERVER_ERROR),
  IDENTITY_GRAPHQL_API_CALL_FAILED("W142", "Failure while invoking Identity Graphql API", "Failure while invoking Identity Graphql API", HttpStatus.INTERNAL_SERVER_ERROR),
  IDENTITY_INVALID_RESPONSE_RECEIVED("W142", "Invalid response received from Identity Service", "Invalid response received from Identity Service", HttpStatus.INTERNAL_SERVER_ERROR),
  POPULATE_CACHE_FAILED("W1002", "Error in populating cache",
          "Cache insertion/update failed", HttpStatus.INTERNAL_SERVER_ERROR),
  REDIS_CACHE_ERROR("W1002", "Error in Redis operation",
          "Error occurred during Redis operation", HttpStatus.INTERNAL_SERVER_ERROR),
  REDIS_CONNECTION_ERROR("W1002", "Error in connecting to Redis",
          "Error occurred while trying to connect to Redis during cache operation", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_LIMIT("W143","Invalid Limit. Min %s and Max of %s allowed", "Invalid limit provided for Pagination",
          HttpStatus.BAD_REQUEST),
  ERROR_ADDING_TRIGGER_TO_EVENT("W144","Error adding trigger api events to eventbus", "Error adding trigger api events to eventbus",
          HttpStatus.BAD_REQUEST),
  ITM_GRAPHQL_CLIENT_FAILURE("W145", "Error in create/update/read itm task from graphql client. Error=%s",
          "ITM Graphql Client Exception", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.TASK_SERVICE, DownstreamServiceName.TASK_SERVICE_CREATE_UPDATE_TASK),
  HUMAN_TASK_ITM_DOWNSTREAM_FAILURE("W146", "Error in create/update/read itm task. Error=%s",
          "Create/Update itm task exception", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.TASK_SERVICE, DownstreamServiceName.TASK_SERVICE_CREATE_UPDATE_TASK),
  ITM_REQUEST_HEADERS_MISSING("W147", "Mandatory header for downstream ITM call missing. headerName=%s",
          "ITM Request Mandatory Header Missing", HttpStatus.BAD_REQUEST,
          DownstreamComponentName.TASK_SERVICE, DownstreamServiceName.TASK_SERVICE_CREATE_UPDATE_TASK),
  EXTERNAL_TASK_VALIDATION_ERROR("W148", "Validation failed. error=%s","Validation failed. error %s", HttpStatus.BAD_REQUEST),
  VARIABILITY_GRAPHQL_CLIENT_FAILURE("W149", "Error in querying variability from graphql client. Error=%s",
          "Variability Graphql Client Exception", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.VARIABILITY, DownstreamServiceName.VARIABILITY_SERVICE_QUERY),
  FEATURE_NOT_SUPPORTED("W150","Feature Not Supported","Feature Not Supported",HttpStatus.BAD_REQUEST,DownstreamComponentName.VARIABILITY, DownstreamServiceName.VARIABILITY_SERVICE_QUERY),
  DECISION_DETAILS_NOT_FOUND("W151","Decision details not found","Decision details not found", HttpStatus.BAD_REQUEST),
  INVALID_URL("W152","Invalid url=%s in http request","Invalid url in http request", HttpStatus.BAD_REQUEST),
  //W10xx series for incorrect config errors
  INVALID_OFFERING_ID_CONFIG("W1001","Missing DB/Camunda endpoint config for the offering","Missing DB/Camunda endpoint config for the offering",HttpStatus.INTERNAL_SERVER_ERROR),
  APOLLO_CLIENT_CONFIG_NOT_DEFINED("W1002", "Apollo client config is not defined for serviceName=%s", "Apollo client config is not defined for serviceName=%s", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_PRODUCER_CONFIG("W1003","Missing producer config for kafka","Missing producer config for kafka",HttpStatus.INTERNAL_SERVER_ERROR),


  //W200 series for history api errors
  PROCESS_DETAILS_ERROR("W201", "Error in getting process details. Error=%s",
          "Error in getting process details", HttpStatus.INTERNAL_SERVER_ERROR),
  HISTORY_TTL_ERROR("W202", "History TTL cannot be null or Empty.",
          "History TTL cannot be null or Empty", HttpStatus.BAD_REQUEST, true),
  HISTORY_TTL_THRESHOLD_ERROR("W203", "History Time To Live value is greater than the permissible value.",
          "History Time To Live value is greater than the permissible value", HttpStatus.BAD_REQUEST, true),
  COMPARE_SINGLE_DEFINITION_ERROR("W204", "Error while comparing single definition with user definition", "Error while comparing definitions", HttpStatus.INTERNAL_SERVER_ERROR, true),
  PROCESS_VARIABLE_DETAILS_ERROR("W205", "Error in getting process variable details. Error=%s",
          "Error in getting process variable details", HttpStatus.INTERNAL_SERVER_ERROR),
  EXTERNAL_TASK_LOG_ERROR("W206", "Error in getting external task log details. Error=%s",
          "Error in getting external task log details.", HttpStatus.INTERNAL_SERVER_ERROR),

  //W300 series for rule evaluation error
  RULE_EVALUATION_ERROR("W300", "Error while evaluating rules", WorkflowConstants.RULE_EVALUATE_EXCEPTION,
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.CAMUNDA,
          DownstreamServiceName.CAMUNDA_EVALUATE_RULES),
  RULE_EVALUATION_PAYLOAD_PROCESSING_ERROR("W301",
          "Error while processing payload for rule evaluation", WorkflowConstants.RULE_EVALUATE_EXCEPTION,
          HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_V3_PAYLOAD_PROCESS),
  RULE_EVALUATION_DMN_NOT_FOUND_ERROR("W302", "No matching DMN found for rule evaluation",
          "Dmn not found exception", HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.CAMUNDA,
          DownstreamServiceName.CAMUNDA_EVALUATE_RULES),
  RULE_EVALUATION_CAMUNDA_ERROR("W303",
          "Error from camunda processing payload for rule evaluation. Error=%s",
          WorkflowConstants.RULE_EVALUATE_EXCEPTION, HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_V3_PAYLOAD_PROCESS),

  ASYNC_BEFORE_NOT_SET("W304", "Async before must be set",
          "Async before is required. Missing async before on start event.", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS, DownstreamServiceName.TEMPLATE_DETAILS),

  //W600 series trigger processing errors
  // TODO QBOES-11000 remove these errors after monolith rollout
  TRIGGER_NO_WORKFLOW_DEFINITION_FOUND_ERROR("W600",
          "No workflow definition found for ownerId=%s entityType=%s",
          "No workflow definition enabled exception", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS_DB, DownstreamServiceName.WAS_DB_GET_DEFINITIONS),
  TRIGGER_START_PROCESS_ERROR("W601", "Error in  starting process. Error=%s", "Start process exception",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.CAMUNDA,
          DownstreamServiceName.CAMUNDA_START_PROCESS),
  TRIGGER_SIGNAL_PROCESS_ERROR("W602", "Error in triggering process on the waiting task. Error=%s",
          "Trigger process exception", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.CAMUNDA, DownstreamServiceName.CAMUNDA_TRIGGER_PROCESS),
  TRIGGER_PROCESS_VARIABLE_DETAILS_NOT_FOUND("W603",
          "Process variable details not found in template",
          "Process variable details not found in template error", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS, DownstreamServiceName.WAS_GET_TEMPALTE_PROPERTIES),
  TRIGGER_STARTABLE_EVENTS_DETAILS_NOT_FOUND("W604",
          "Startable events details not found in template",
          "Startable events details not found in template error", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS, DownstreamServiceName.WAS_GET_TEMPALTE_PROPERTIES),
  TRIGGER_NO_VARIABLES_FOUND("W605", "Error in starting process. No variables set.",
          "Start process exception. No variables set.", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.CAMUNDA, DownstreamServiceName.CAMUNDA_START_PROCESS),
  TRIGGER_PROCES_DEFINITION_ERROR("W606", "Error in signalling as no process definition or execution matches the parameters", "Signal process exception", HttpStatus.BAD_REQUEST, DownstreamComponentName.CAMUNDA, DownstreamServiceName.CAMUNDA_TRIGGER_PROCESS),
  PROCESS_VARIABLE_DETAILS_NOT_FOUND("W607", "Error in signalling process as no process variables found.",
          "Signal process exception", HttpStatus.BAD_REQUEST, DownstreamComponentName.CAMUNDA,
          DownstreamServiceName.CAMUNDA_TRIGGER_PROCESS),
  TRIGGER_ERROR("W608", "No action could be taken for trigger event", "Trigger exception",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.CAMUNDA,
          DownstreamServiceName.CAMUNDA_START_PROCESS),
  TRIGGER_MESSAGE_NAME_ERROR("W609", "Error in triggering process on the waiting task. Error=%s",
          "Trigger process exception", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.CAMUNDA, DownstreamServiceName.CAMUNDA_TRIGGER_PROCESS),
  BUSINESS_KEY_NOT_ALLOWED("W610", "Business Key is allowed for system definitions only",
          "Business Key is allowed for system definitions only", HttpStatus.BAD_REQUEST),

  //W7xx series for offline ticket refresh job
  READER_CREATION_FAILURE("W700",
          "Item reader creation for realm offline ticket refresh job failed",
          "Could not create the ItemReader object", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS_DB, DownstreamServiceName.REALM_OFFLINE_TICKET_READER),
  FAILED_TO_SAVE_OFFLINE_TICKET("W701", "Could not save the realm offline ticket in datastore",
          "Saving the realm offline ticket in database failed", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS_DB, DownstreamServiceName.REALM_OFFLINE_TICKET_WRITE_LISTENER),
  FAILED_TO_CREATE_TICKET("W702", "Creation of realm offline ticket failed",
          "Exception while trying to create realm offline ticket", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.OFFLINE_CLIENT, DownstreamServiceName.CREATE_REALM_OFFLINE_TICKET),
  INVALID_DATE_FORMAT("W703", "Invalid date format", "Exception while parsing date",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.DATE_UTILS),

  DEFINITION_TYPE_UPDATE_ERROR("W800", "Cannot change the definition type",
          "Change of definition type is not allowed", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS, DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE),
  TEMPLATE_CONTAINS_DUPLICATE_TRIGGER_NAMES("W801", "Template contains duplicate trigger messages", "Template contains duplicate trigger messages",
          HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS_DB,
          DownstreamServiceName.TEMPLATE_DETAILS),
  INVALID_RECURRENCE_PARAMETER("W802", "Invalid parameter value for recurrence rule", "Unsupported value in recurrence rule parameters",
          HttpStatus.INTERNAL_SERVER_ERROR),
  RECURRENCE_NOT_SUPPORTED("W803", "Recurrence Unsupported for Non-Custom Templates", "Recurrence Unsupported for Non-Custom Templates",
          HttpStatus.INTERNAL_SERVER_ERROR),

  //OAuth2 token validation erros
  USER_NOT_REALM_MEMBER("W850", "USER_NOT_REALM_MEMBER.",
          "User is not a member of the specified Realm", HttpStatus.BAD_REQUEST,
          DownstreamComponentName.WAS, DownstreamServiceName.USER_NOT_REALM_MEMBER),
  INVALID_IAM_TICKET("W851", "INVALID_IAM_TICKET.",
          "Ticket verification denied: 000 Success DENIED (expired)", HttpStatus.BAD_REQUEST,
          DownstreamComponentName.WAS, DownstreamServiceName.INVALID_IAM_TICKET),
  TICKET_VERIFICATION_FAILED("W852", "TICKET_VERIFICATION_FAILED.",
          "Ticket verification failed", HttpStatus.BAD_REQUEST,
          DownstreamComponentName.WAS, DownstreamServiceName.TICKET_VERIFICATION_FAILED),

  UNAUTHORIZED_RESOURCE_ACCESS(
          "W853",
          "UNAUTHORIZED_RESOURCE_ACCESS.",
          "User doesn't have access to requested resource.%s",
          HttpStatus.UNAUTHORIZED,
          DownstreamComponentName.WAS,
          DownstreamServiceName.UNAUTHORIZED_RESOURCE_ACCESS),
  FAILED_TO_INITIALIZE_AUTHZ_CLIENT("W854",
          "FAILED_TO_INITIALIZE_AUTHZ_CLIENT",
          "Failed to Initialise AuthZ Client",
          HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS,
          DownstreamServiceName.AUTHZ_CLIENT_INITIALIZATION),
  EXTENSION_ATTRIBUTES_MUTATION_ERROR(
          "W855",
          "Extension Elements cannot be mutated. Invalid attributes=%s",
          "Extension Elements cannot be mutated.",
          HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_DB_TASK_MUTATION),

  EXTERNAL_TASK_NOT_FOUND(
          "W856",
          "External Task is already completed.",
          "Task cannot be mutated as it is already completed.",
          HttpStatus.NOT_FOUND,
          DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_DB_TASK_MUTATION),
  AUTHZ_REQUEST_FAILED("W857", "Authz request failed.",
          "Authz request failed.",
          HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS,
          DownstreamServiceName.AUTHZ_CLIENT_REQUEST),



  // W9xx series for eventing related errors
  MISSING_EVENT_HEADERS("W900", "Event with missing or incorrect headers recieved.%s.",
          WorkflowConstants.PROCESSING_EVENT_FAILED, HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_EVENT_PROCESSOR, true),
  INCORRECT_EVENT_PAYLOAD("W901", "Incorrect event payload recieved.%s", WorkflowConstants.PROCESSING_EVENT_FAILED,
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_EVENT_PROCESSOR, true),
  DUPLICATE_EVENT("W902", "Duplicate event recieved", "Duplicate event will not be processed",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_EVENT_PROCESSOR),
  INVALID_EVENT_CONFIG_ERROR("W903", "Incorrect Eventing Config. Error=%s",
          "Incorrect Eventing Config", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_EVENT_TOPIC_ERROR("W904", "Invalid Topic Name", "Invalid Topic Name",
          HttpStatus.INTERNAL_SERVER_ERROR),
  PROCESS_DETAILS_NOT_FOUND_ERROR("W905", "Process Details not found", "Process Details not found",
          HttpStatus.INTERNAL_SERVER_ERROR),
  MISSING_TOPIC_ENTITY_MAPPING("W906", "Event missing topic to entity type mapping",
          "Processing event failed due to missing mapping", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS, DownstreamServiceName.WAS_EVENT_PROCESSOR),
  MISSING_TOPIC_NAME("W907", "Kafka topic not found in header", "Kafka topic not found in header",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_EVENT_PROCESSOR),
  MISSING_HANDLER_ID("W908", "Handler ID not found in Worker Action Request",
          "Handler ID not found in Worker Action Request", HttpStatus.INTERNAL_SERVER_ERROR),
  KAFKA_PUBLISH_ERROR("W909", "Error in publishing event to kafka",
          "Error in publishing event to kafka", HttpStatus.INTERNAL_SERVER_ERROR),
  WAS_DB_CONNECTION_ERROR("W910", "WAS DB connection error", "WAS DB connection error",
          HttpStatus.INTERNAL_SERVER_ERROR),
  EVENT_RETRIES_FAILED("W911", "Event consumption failed post retries", WorkflowConstants.PROCESSING_EVENT_FAILED,
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_EVENT_PROCESSOR),
  MISSING_CONSUMER_RECORD_DETAILS_RETRY_CONTEXT("W912",
          "Missing details of consumer record in retry context",
          "Processing event failed-error in recovery callback", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS, DownstreamServiceName.WAS_EVENT_PROCESSOR),
  MISSING_ACKNOWLEDGEMENT_DETAILS_RETRY_CONTEXT("W913",
          "Missing details of acknowledgement in retry context",
          "Processing event failed-error in recovery callback", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS, DownstreamServiceName.WAS_EVENT_PROCESSOR),
  MISSING_TEMPLATE_ID("W914", "Template ID not present in Template Details",
          "Template ID not present in Template Details", HttpStatus.INTERNAL_SERVER_ERROR),
  ONLY_SYSTEM_DEFINITION_IS_SUPPORTED("W915", "Only system definition is supported.",
          "Only system definition is supported", HttpStatus.INTERNAL_SERVER_ERROR),
  UPDATE_EXECUTION_VARIABLE_FAILED("W916", "Execution Variable update call failed",
          "Execution Variable update call failed", HttpStatus.INTERNAL_SERVER_ERROR),
  EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR("W917", "External task details not found error",
          "External task details not found error", HttpStatus.INTERNAL_SERVER_ERROR),
  DMN_EVALUATE_ERROR("W304", "Error while evaluating DMN", "DMN evaluation error",
          HttpStatus.INTERNAL_SERVER_ERROR, DownstreamComponentName.WAS,
          DownstreamServiceName.EVALUATE_DMN),
  INVALID_ROLLBACK_PARAMETER("W918", "Invalid Migration parameters", "Invalid Migration parameters",
          HttpStatus.INTERNAL_SERVER_ERROR),
  CAMUNDA_UPDATE_PROCESS_INSTANCE_VARIABLE_FAILED("W919", "Process Instance Variable update call failed",
          "Process Instance Variable update call failed", HttpStatus.INTERNAL_SERVER_ERROR),
  PROCESS_ENGINE_RETRY_EXHAUSTED("W920", "Camunda Process Engine Exception Retries exhausted",
          "Camunda Process Engine Exception Retries exhausted", HttpStatus.INTERNAL_SERVER_ERROR),
  CAMUNDA_PROCESS_INSTANCE_DELETE_FAILED("W921", "Process Instance delete call failed",
          "Process Instance delete call failed", HttpStatus.INTERNAL_SERVER_ERROR),
  TASK_FAIL_WITH_RETRIES_EXHAUSTED("W922", "Number of retries exhausted",
          "Number of retries have exhausted for the task. Creating incident.", HttpStatus.INTERNAL_SERVER_ERROR),
  CLEANUP_FAILED_PROCESS_IN_ACTIVE("W1002",
          "Process is not completed",
          "Process is not completed , Its in Active/Error State",
          HttpStatus.INTERNAL_SERVER_ERROR),
  DISABLE_FAILED_PROCESS_IN_ACTIVE("W1003",
          "Process is not completed",
          "Process is not completed , Its in Active/Error State",
          HttpStatus.INTERNAL_SERVER_ERROR),
  DEFINITION_MIGRATION_FAILED("W1003", "Could not complete migration", "Migration incomplete", HttpStatus.INTERNAL_SERVER_ERROR),
  APPCONNECT_DUZZIT_CALL_FAILURE(
          "W1004",
          "Exception occurred in appconnect duzzit=%s.Error=%s",
          "Exception occurred in appconnect duzzit", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_TEMPLATE_TAG_VERSION("W1005","Invalid template tag version, please use sematic version", "Invalid template tag version, please use sematic version", HttpStatus.BAD_REQUEST ),
  DUPLICATE_TEMPLATE_AND_TAG("W1006","Duplicate template with given tag name and version", "Please try again with different tag version", HttpStatus.BAD_REQUEST ),
  UPDATE_FAILED_DUE_TO_TAG_VERSION_REMOVAL("W1007","Update failed due to tag version removal", "Please try again with a proper tag version", HttpStatus.BAD_REQUEST ),
  TEMPLATE_TAG_NOT_SUPPORTED("W1008","System Tags not supported for this template", "Tag Version is only supported  for SYSTEM Definitions", HttpStatus.BAD_REQUEST ),
  NO_EXISTING_TEMPLATE_WITH_TAG("W1009", "No Template has matching tags", "Invalid template tag for given payload", HttpStatus.BAD_REQUEST),
  NO_EXISTING_DEFINITION_WITH_TAG("W1010", "No Definition has matching tags", "Invalid definition with tag as provided in payload", HttpStatus.BAD_REQUEST),
  STEPS_NOT_FOUND_IN_TEMPLATE_CONFIG("W1011", "No workflow steps details found in template config", "No workflow steps details found in template config", HttpStatus.INTERNAL_SERVER_ERROR),
  ACTION_ID_MAPPER_NOT_FOUND_IN_TEMPLATE_CONFIG("W1012", "No actionIdMapper was found in template config", "No actionIdMapper was found in template config", HttpStatus.INTERNAL_SERVER_ERROR),
  EVENT_SCHEDULE_CALL_FAILURE("W1011", "Exception occurred in making call to ESS.Error=%s", "Exception occurred in making call to ESS", HttpStatus.INTERNAL_SERVER_ERROR),
  //Activity errors
  DEFINITION_ACTIVITY_DETAILS_NOT_FOUND("W1013", "Activity details not found", "Activity details not found", HttpStatus.BAD_REQUEST),
  DEFINITION_ACTIVITY_ATTRIBUTE_MISSING("W1014", "Activity user attribute not found", "Activity user attribute not found", HttpStatus.BAD_REQUEST),
  INVALID_ACTIVITY_ID("W1015", "Activity Id is missing", "Activity id is not present", HttpStatus.BAD_REQUEST),

  INVALID_PROCESS_DETAILS("W1016", "Process details are missing", "Process details are missing", HttpStatus.BAD_REQUEST),
  INVALID_ROOT_PROCESS_INSTANCE_ID("W1017", "Root process instance id is missing", "Root process instance id is missing", HttpStatus.BAD_REQUEST),

  DECISION_TABLE_MISSING("W1018", "Decision table element is missing", "Decision table element is not present during Dmn creation", HttpStatus.BAD_REQUEST),
  INVALID_WORKFLOW_STEP("W1019", "Workflow step is invalid", "Workflow step is either invalid or the step type for the workflow step is empty", HttpStatus.BAD_REQUEST),
  INVALID_BPMN_ELEMENT("W1020", "BPMN element type is invalid", "BPMN element type is invalid", HttpStatus.INTERNAL_SERVER_ERROR),
  DMN_READ_FAILURE("W1021", "DMN read failure", "DMN read failure", HttpStatus.INTERNAL_SERVER_ERROR),
  MULTI_STEP_DEFINITION_SAVE_EXCEPTION("W1022", "Exception while saving multi-step definition in DB",
          "Exception while saving the multi step definition in database", HttpStatus.INTERNAL_SERVER_ERROR),
  CONDITION_WORKFLOW_STEP_NOT_FOUND("W1023", "Conditional step details are missing",
          "Conditional step details missing exception", HttpStatus.BAD_REQUEST),
  APPROVAL_WORKFLOWS_STEPS_LIMIT_EXCEEDED("W1024", "Number of steps is more than the allowed limit",
          "Number of steps is more than the allowed limit", HttpStatus.BAD_REQUEST),
  MISSING_NEXT_PATH_ERROR("W1025", "Missing yes or no path in the condition",
          "Missing yes or no path in the condition", HttpStatus.BAD_REQUEST),
  CYCLIC_WORKFLOW_STEPS_NOT_ALLOWED("W1026", "Cycles in workflow steps are not allowed",
          "Cyclic events are not allowed", HttpStatus.BAD_REQUEST),
  CALL_ACTIVITY_TEMPLATE_NOT_FOUND("W1027", "Call activity template not found in database",
          "Call activity template not found in database", HttpStatus.BAD_REQUEST),
  INVALID_CALLED_ELEMENT_BINDING("W1028", "Invalid called element binding found.", "Invalid called element binding " +
          "found.",
          HttpStatus.BAD_REQUEST),
  CALL_ACTIVITY_ROOT_PROCESS_INSTANCE_ID_NOT_FOUND("W1029", "Process variable rootProcessInstanceId is not found.",
          "Process variable rootProcessInstanceId is not found.", HttpStatus.BAD_REQUEST),
  EMPTY_INPUT_OUTPUT_IN_CALL_ACTIVITY("W1030", "Call activity input output fields are empty.",
          "Call activity input output fields are empty.", HttpStatus.BAD_REQUEST),
  PARENT_TEMPLATE_AND_CHILD_CALL_ACTIVITY_CONTAINS_DUPLICATE_TRIGGER_NAMES("W1031",
          "Parent Bpmn and child called activity element bpmn contains duplicate trigger messages",
          "Parent Bpmn and child called activity element bpmn contains duplicate trigger messages",
          HttpStatus.BAD_REQUEST, DownstreamComponentName.WAS_DB,
          DownstreamServiceName.TEMPLATE_DETAILS),
  UNUSED_WORKFLOW_STEP("W1032",
          "The workflow step id present in the next path of condition does not have a workflow step associated with it.",
          "The workflow step id present in the next path of condition does not have a workflow step associated with it.",
          HttpStatus.BAD_REQUEST),
  MUTATION_NOT_SUPPORTED("W1033","Mutation is not supported for Workflow Task Type=%s" ,"Mutation is not supported for  Workflow Task Type=Milestone" ,HttpStatus.BAD_REQUEST ),
  APPROVAL_WORKFLOWS_APPROVERS_LIMIT_EXCEEDED("W1034", "Number of approvers is more than the allowed limit",
          "Number of approvers is more than the allowed limit", HttpStatus.BAD_REQUEST),
  DMN_NOT_FOUND_ERROR("W1035", "No matching DMN found",
          "Dmn not found exception", HttpStatus.BAD_REQUEST,true),
  APPCONNECT_DUZZIT_RETRYABLE_EXCEPION(
          "W1036",
          "Retryable exception occurred in appconnect duzzit error=%s",
          "Retryable exception occurred in appconnect duzzit", HttpStatus.INTERNAL_SERVER_ERROR),
  SAVE_SCHEDULE_DETAILS_EXCEPTION("W1037",
          "Exception occurred in saving data to schedule details.Error=%s",
          "Exception occurred in saving data to schedule details", HttpStatus.INTERNAL_SERVER_ERROR),
  SCHEDULE_DETAILS_NOT_FOUND("W1038", "Schedules not found", "Invalid id", HttpStatus.BAD_REQUEST, true),
  ESS_MIGRATION_CREATE_SCHEDULE_TASKS("W1039", "create schedule tasks not created", "create schedule tasks not created", HttpStatus.BAD_REQUEST, true),
  SCHEDULE_IDS_EMPTY("W1040",  "Schedules Ids are empty", "Schedules Ids are empty", HttpStatus.BAD_REQUEST, true),
  PROCESS_DETAILS_CONSTRAINT_VIOLATION_ERROR("W1041",  "Unable to save Process details", "Unable to save Process details", HttpStatus.BAD_REQUEST, true),
  PROGRESS_TRACKING_MILESTONE_NOT_FOUND("W1042","Milestone Data not found in extensions", "Milestone Data not found in extensions", HttpStatus.NOT_FOUND, true),
  DELETE_STALE_DEFINITION_FAIL("W1043", "Error in Deleting stale definitions",
          "Delete Stale Definition error", HttpStatus.INTERNAL_SERVER_ERROR),
  CREATION_NOT_SUPPORTED("W1044", "Workflow creation not supported",
          "Workflow creation not supported", HttpStatus.BAD_REQUEST, true),
  TRIGGER_NO_ACTION("W1045", "No action could be taken for trigger event", "Trigger exception",
          HttpStatus.NO_CONTENT, DownstreamComponentName.CAMUNDA,
          DownstreamServiceName.CAMUNDA_START_PROCESS, true),
  JIRA_API_CALL_FAILURE(
          "W1046",
          "Exception occurred in jira api.Error=%s",
          "Exception occurred in jira api", HttpStatus.INTERNAL_SERVER_ERROR),
  SLACK_API_CALL_FAILURE(
          "W1047",
          "Exception occurred in slack api api.Error=%s",
          "Exception occurred in slack api", HttpStatus.INTERNAL_SERVER_ERROR),
  COMPOSITE_WORKFLOW_STEP_NOT_LEAF_NODE("W1048", "Composite workflow step is not a leaf node",
          "Composite workflow step is not a leaf node", HttpStatus.BAD_REQUEST),
  CALLED_ELEMENT_ACTION_KEY_MISMATCH("W01049", "CallActivity element not found for the given actionKey",
          "CallActivity element not found for the given actionKey", HttpStatus.INTERNAL_SERVER_ERROR),
  DOWNSTREAM_CALL_FAILURE("W01050", "Downstream call failed", "Downstream call failed", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_CONDITIONAL_EXPRESSION("W01051","Invalid conditional expression", "Invalid conditional expression", HttpStatus.BAD_REQUEST),
  FILTER_CLOSE_TASK_ACTION_NOT_FOUND(
          "W01052", "Filter Close task action not found", "Filter Close task action not found", HttpStatus.BAD_REQUEST),
  MISSING_WORKER_ID("W1053", "No topic details found for the given Worker Id",
          "No topic details found for the given Worker Id", HttpStatus.INTERNAL_SERVER_ERROR),
  EVENT_SCHEDULING_CALL_FAILURE("W1054", "Exception occurred in making call to Scheduling Service.Error=%s", "Exception occurred in making call to Scheduling Service", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_REFERENCE_ID("W1055", "Invalid reference id", "Invalid reference id", HttpStatus.BAD_REQUEST),
  ERROR_CAUSE_NOT_FOUND("W1056", "Error cause not found for the errored event", "Error cause not found for the errored event", HttpStatus.BAD_REQUEST),
  INVALID_OWNER_ID("W1057", "Invalid Owner Id", "Invalid Owner Id passed", HttpStatus.BAD_REQUEST),
  FILTER_PARAMS_NOT_FOUND("W1058", "Filter params are empty or not found", "Filter params for a DB operation is mandatory", HttpStatus.INTERNAL_SERVER_ERROR),
  SCHEDULING_SERVICE_BAD_REQUEST_FAILURE("SCHEDULING_ERROR_%s", "Bad request occurred in Scheduling Service.Error=%s", "Exception occurred in making call to Scheduling Service", HttpStatus.BAD_REQUEST, true),
  SCHEDULING_SERVICE_CALL_FAILURE("SCHEDULING_ERROR", "Unexpected response received from Scheduling Service.Error=%s", "Unexpected response received from Scheduling Service", HttpStatus.INTERNAL_SERVER_ERROR),
  UPDATE_SCHEDULE_DETAILS_EXCEPTION("W1059",
          "Exception occurred in updating data to schedule details.Error=%s",
          "Exception occurred in updating data to schedule details", HttpStatus.INTERNAL_SERVER_ERROR),
  OBJECT_OPTIMISTIC_LOCKING_FAILURE("W1060", "Object optimistic locking failure .%s.",
          "Object optimistic locking failure", HttpStatus.INTERNAL_SERVER_ERROR,
          DownstreamComponentName.WAS,
          DownstreamServiceName.WAS_EVENT_PROCESSOR, true),
  SCHEDULING_MIGRATION_FAILED("W1061", "Could not complete scheduling migration", "Could not complete scheduling migration", HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_ENTITY_TYPE("W1062", "Entity type cannot be null or empty.", "Entity type cannot be null or empty.",
          HttpStatus.INTERNAL_SERVER_ERROR),
  EVENT_NOT_SUPPORTED("W01062", "State transition event not supported in child BPMN start event.",
          "State transition event not supported in child BPMN start event.", HttpStatus.INTERNAL_SERVER_ERROR),
  TXN_DETAILS_NOT_FOUND_FOR_ACTIVITY_ID("W1063",
          "Transaction details not found for activityId.",
          "Transaction details not found for activityId", HttpStatus.BAD_REQUEST),
  TRIGGER_NOW_ASYNC_TASK_ERROR("W1064", "Exception in trigger now sync task",
          "Exception in trigger now async task", HttpStatus.INTERNAL_SERVER_ERROR);


  private static final Map<String, WorkflowError> workflowErrorMap = getWorkflowErrorMap();
  private final String errorCode;
  private final String errorDescription;
  private final HttpStatus status;
  @Setter
  private String errorMessage;
  private DownstreamComponentName downstreamComponentName;
  private DownstreamServiceName downstreamServiceName;
  private boolean isWarning;

  private WorkflowError(
          final String errorCode, final String errorMessage, final String errorDescription,
          final HttpStatus status) {

    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
    this.errorDescription = errorDescription;
    this.status = status;
  }

  private WorkflowError(
          final String errorCode,
          final String errorMessage,
          final String errorDescription,
          final HttpStatus status,
          final DownstreamComponentName downstreamComponentName,
          final DownstreamServiceName downstreamServiceName) {
    this(errorCode, errorMessage, errorDescription, status);
    this.downstreamComponentName = downstreamComponentName;
    this.downstreamServiceName = downstreamServiceName;
  }

  private WorkflowError(
          final String errorCode, final String errorMessage, final String errorDescription,
          final HttpStatus status, final boolean isWarning) {
    this(errorCode, errorMessage, errorDescription, status);
    this.isWarning = isWarning;
  }

  private WorkflowError(
          final String errorCode,
          final String errorMessage,
          final String errorDescription,
          final HttpStatus status,
          final DownstreamComponentName downstreamComponentName,
          final DownstreamServiceName downstreamServiceName,
          final boolean isWarning) {
    this(errorCode, errorMessage, errorDescription, status, downstreamComponentName, downstreamServiceName);
    this.isWarning = isWarning;
  }

  private static Map<String, WorkflowError> getWorkflowErrorMap() {

    final Map<String, WorkflowError> possibleWorkflowErrorMap = new HashMap<>();
    for (final WorkflowError workflowError : values()) {
      possibleWorkflowErrorMap.put(workflowError.name(), workflowError);
    }
    return possibleWorkflowErrorMap;
  }

  public static WorkflowError value(final String type) {
    return workflowErrorMap.getOrDefault(type, WorkflowError.UNKNOWN_ERROR);
  }
}