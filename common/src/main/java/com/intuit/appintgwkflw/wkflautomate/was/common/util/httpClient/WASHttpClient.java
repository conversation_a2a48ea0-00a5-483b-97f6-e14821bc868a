package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.WAS_REST_TEMPLATE;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASHttpClientConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.CommonExecutor;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Optional;


/**
 * <AUTHOR>
 *     <p>class acts as http client and all specific http call api resides here.
 */
@Component
public class WASHttpClient {

  @Qualifier(WAS_REST_TEMPLATE)
  @Autowired
  private RestTemplate restTemplate;

  @Autowired
  private WASHttpClientConfiguration wasHttpClientConfiguration;

  /**
   * @param <REQUEST> Request type
   * @param <RESPONSE> Response type
   * @param wasHttpRequest was http request that can handle all sort of get,post request
   * @return {@link WASHttpResponse<RESPONSE>}
   */
  public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> httpResponse(
          WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest ) {
    return CommonExecutor.executeRestSupplier(
            () ->
                    restTemplate.exchange(
                            validateAndGetUrl(wasHttpRequest.getUrl()),
                            wasHttpRequest.getHttpMethod(),
                            wasHttpRequest.getRequestEntity(),
                            wasHttpRequest.getResponseType()), wasHttpRequest.getRetryHandler());
  }

  /**
   * prepare {@link WASHttpResponse} by setting response and status in the response and setting
   * error if downstream sends non 200 response.No need for caller to surround code with try catch.
   *
   * @param url input rest post actionUrl
   * @param requestEntity Type HttpEntity with headers and body
   * @return return WASHttpResponse
   */
  public <T> WASHttpResponse<T> getResponse(
          String url, @SuppressWarnings("rawtypes") HttpEntity requestEntity, Class<T> responseType, RetryHandlerName retryHandler) {
    return CommonExecutor.executeRestSupplier(
            () -> restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType),retryHandler);
  }

  /**
   * @param url input rest post actionUrl
   * @param requestEntity Type HttpEntity with headers and body
   * @return prepare {@link WASHttpResponse} by setting response and status in the response and
   *     setting error if downstream sends non 200 response.No need for caller to surround code with
   *     try catch.
   */
  public <T> WASHttpResponse<T> postResponse(
          String url, HttpEntity<?> requestEntity, Class<T> responseType, RetryHandlerName retryHandler) {
    return CommonExecutor.executeRestSupplier(
            () -> restTemplate.exchange(url, HttpMethod.POST, requestEntity, responseType),retryHandler);
  }

  /**
   * Validate if the domain is whitelisted
   * @param url - request url
   * @return
   */
  private String validateAndGetUrl(String url) {
    String host = UriComponentsBuilder.fromHttpUrl(url).build().getHost(); // This will throw an exception if the URL is malformed
    return Optional.ofNullable(host)
            .filter(i -> wasHttpClientConfiguration.getWhitelistedDomains().stream().anyMatch(
                    i::contains
            )).orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_URL, url));
  }
}